# Java Scaffold 项目文档

## 项目概述

Java Scaffold 是一个通用的Java项目脚手架，基于Spring Boot + MyBatis架构，支持SQLite和PostgreSQL数据库。该脚手架提供了完整的分层架构设计、全局异常处理、统一响应格式、API文档等功能，可以快速启动新的Java Web项目。

### 主要功能

- **分层架构**：完整的Controller → Service → DAO → Entity分层设计
- **数据库支持**：支持SQLite和PostgreSQL，自动初始化数据库
- **全局异常处理**：统一的异常处理机制
- **统一响应格式**：标准化的API响应格式
- **API文档**：集成Swagger，自动生成API文档
- **日志管理**：完善的Logback日志配置
- **Docker支持**：完整的Docker镜像构建和部署机制
- **用户管理示例**：完整的用户CRUD操作示例

## 技术栈

### 核心框架
- **Spring Boot**: 2.3.7.RELEASE
- **Java**: 8
- **Maven**: 项目构建工具

### 数据库
- **SQLite**: ******** (默认数据库)
- **PostgreSQL**: 42.3.8 (可选数据库)
- **MyBatis**: 2.1.4 (ORM 框架)

### 工具库
- **Lombok**: 1.18.24 (代码简化)
- **FastJSON**: 2.0.45 (JSON 处理)
- **Commons Lang3**: 3.8.1 (工具类)
- **Swagger**: 2.9.1 (API 文档)

## 快速开始

### 环境要求

#### 开发环境
- **JDK**: 1.8 或以上
- **Maven**: 3.6 或以上
- **IDE**: IntelliJ IDEA 或 Eclipse

#### 运行环境
- **Java Runtime**: 1.8 或以上
- **SQLite**: 数据库文件（默认）
- **PostgreSQL**: 可选数据库

### 构建和运行

#### 1. 克隆项目
```bash
git clone <repository-url>
cd java-scaffold
```

#### 2. 编译项目
```bash
mvn clean compile
```

#### 3. 运行测试
```bash
mvn test
```

#### 4. 打包应用
```bash
mvn clean package
```

#### 5. 运行应用
```bash
# 开发模式
mvn spring-boot:run

# 或运行打包后的jar
java -jar target/java-scaffold.jar
```

**注意**: 首次启动时，应用会自动创建数据库文件和表结构，无需手动配置。

#### 6. 访问应用
- **应用地址**: http://localhost:28080
- **API文档**: http://localhost:28080/swagger-ui.html

## 配置说明

### 应用配置 (application.yml)

```yaml
server:
  port: 28080

spring:
  profiles:
    active: sqlite  # 默认使用SQLite
  datasource:
    url: ${DATABASE_URL:**********************************}
    driver-class-name: org.sqlite.JDBC
    username: ${DATABASE_USERNAME:}
    password: ${DATABASE_PASSWORD:}
```

### 数据库切换

#### 使用PostgreSQL
1. 修改application.yml中的profiles.active为postgresql
2. 配置PostgreSQL连接信息：
```yaml
spring:
  profiles:
    active: postgresql
  datasource:
    url: *****************************************
    driver-class-name: org.postgresql.Driver
    username: scaffold
    password: scaffold123
```

### 环境变量配置

```bash
# SQLite配置
export DATABASE_URL=**********************************

# PostgreSQL配置
export DATABASE_URL=*****************************************
export DATABASE_USERNAME=scaffold
export DATABASE_PASSWORD=scaffold123
```

## API接口

### 用户管理接口

#### 创建用户
- **URL**: `POST /scaffold/api/users`
- **参数**: username, email, phone, status
- **响应**: 创建的用户信息

#### 分页查询用户列表
- **URL**: `POST /scaffold/api/users/query`
- **参数**: 分页和搜索参数（UserQueryDTO）
- **响应**: 用户列表和总数

#### 根据ID查询用户
- **URL**: `GET /scaffold/api/users/detail`
- **参数**: `userId` - 用户ID
- **响应**: 用户详细信息

#### 根据用户名查询用户
- **URL**: `GET /scaffold/api/users/username`
- **参数**: `username` - 用户名
- **响应**: 用户信息

#### 更新用户信息
- **URL**: `PUT /scaffold/api/users`
- **参数**: 用户信息（UserDTO）
- **响应**: 更新的用户信息

#### 删除用户
- **URL**: `DELETE /scaffold/api/users`
- **参数**: `userId` - 用户ID
- **响应**: 删除结果

## Docker部署

### 构建镜像
```bash
mvn clean package
docker build -t java-scaffold:1.0.0 .
```

### 运行容器
```bash
docker run -d \
  --name java-scaffold \
  -p 28080:28080 \
  -v /var/scaffold/database:/database \
  -v /var/scaffold/logs:/logs \
  -e DATABASE_URL=********************************** \
  java-scaffold:1.0.0
```

### Docker Compose
```bash
docker-compose up -d
```

## 开发指南

### 代码规范
- 使用 Lombok 简化代码
- 遵循 RESTful API 设计原则
- 统一异常处理和响应格式
- 完善的日志记录

### 数据库操作
- 使用 MyBatis 进行数据访问
- 支持事务管理
- 软删除机制（is_deleted 字段）

### 扩展开发
1. 在entity包中创建新的实体类
2. 在dao包中创建对应的DAO接口
3. 在resources/mapper中创建MyBatis映射文件
4. 在service包中创建服务接口和实现
5. 在controller包中创建控制器
6. 更新数据库初始化脚本

## 项目特色

- **开箱即用**: 完整的项目结构和配置
- **多数据库支持**: 灵活的数据库选择
- **自动初始化**: 数据库和表结构自动创建
- **完整示例**: 用户管理功能作为开发参考
- **Docker就绪**: 完整的容器化部署方案
- **文档完善**: 详细的API文档和使用说明

## 许可证

MIT License
