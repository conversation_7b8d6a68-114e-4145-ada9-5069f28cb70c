<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 项目信息 -->
    <groupId>com.cet.electric</groupId>
    <artifactId>QuickStart</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>QuickStart</name>
    <description>通用Java项目脚手架，基于Spring Boot + MyBatis + SQLite/PostgreSQL</description>

    <!-- 通用属性配置 -->
    <properties>
        <java.version>8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 依赖版本管理 -->
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <lombok.version>1.18.24</lombok.version>
        <swagger.version>2.9.1</swagger.version>
        <fastjson.version>2.0.45</fastjson.version>
        <commons-lang3.version>3.8.1</commons-lang3.version>
        <commons-io.version>2.11.0</commons-io.version>
        <mybatis.version>2.1.4</mybatis.version>
        <sqlite-jdbc.version>********</sqlite-jdbc.version>
        <postgresql.version>42.3.8</postgresql.version>
        <logstash-logback.version>5.2</logstash-logback.version>
        <junit.version>4.12</junit.version>
        <mockito.version>2.9.0</mockito.version>
        <powermock.version>1.7.4</powermock.version>
        <docker.maven.plugin.version>0.38.0</docker.maven.plugin.version>
        <docker.registry>*************</docker.registry>
        <docker.image.name>${docker.registry}/base/${project.artifactId}</docker.image.name>
    </properties>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 项目依赖 -->
    <dependencies>
        <!-- Spring Boot 相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 数据库相关 -->
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>${sqlite-jdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis.version}</version>
        </dependency>

        <!-- 日志相关 -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback.version}</version>
        </dependency>

        <!-- 工具类库 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>



        <!-- API文档 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.version}</version>
        </dependency>

        <!-- 测试相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <!-- 构建配置 -->
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <parameters>true</parameters>
                    <compilerArgs>
                        <!-- 过期的方法的警告-->
                        <arg>-Xlint:deprecation</arg>
                    </compilerArgs>
                    <compilerArguments>
                        <!-- 是否输出所有的编译信息（包括类的加载等）-->
                        <!--<verbose />-->
                        <!-- 解决maven命令编译报错，因为rt.jar 和jce.jar在jre的lib下面，不在jdk的lib下面，导致maven找不到（java7以后会出现这个问题），将这2个jar包拷贝到jdk的lib下面估计也好使-->
                        <bootclasspath>${java.home}\lib\rt.jar;${java.home}\lib\jce.jar</bootclasspath>
                    </compilerArguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>com.cet.electric.quickstart.QuickStartApplication</mainClass>
                    <executable>true</executable>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Docker Maven Plugin -->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>${docker.maven.plugin.version}</version>
                <configuration>
                    <!-- Docker 仓库配置 -->
                    <registry>${docker.registry}</registry>
                    <images>
                        <image>
                            <name>${docker.image.name}:${project.version}</name>
                            <build>
                                <dockerFileDir>${project.basedir}</dockerFileDir>
                                <dockerFile>Dockerfile</dockerFile>
                                <args>
                                    <artifactId>${project.artifactId}</artifactId>
                                    <VERSION>${project.version}</VERSION>
                                    <base_image>*************/base/openjdk-with-fontconfig:8-jdk-dbg-alpine</base_image>
                                </args>
                                <assembly>
                                    <inline>
                                        <files>
                                            <file>
                                                <source>${project.build.directory}/${project.build.finalName}.jar</source>
                                                <destName>${project.artifactId}.jar</destName>
                                                <outputDirectory>target</outputDirectory>
                                            </file>
                                        </files>
                                    </inline>
                                </assembly>
                            </build>
                        </image>
                    </images>
                    <!-- 移除认证配置，让插件自动使用 Docker 本地认证 -->
                </configuration>
                <executions>
                    <execution>
                        <id>build-image</id>
                        <phase>package</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>push-image</id>
                        <phase>install</phase>
                        <goals>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Deploy Plugin - 跳过 JAR 部署，只推送 Docker 镜像 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 分发管理 - Maven 仓库配置 -->
    <distributionManagement>
        <repository>
            <id>*************_9082</id>
            <url>http://*************:9082/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>*************_9082</id>
            <url>http://*************:9082/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
